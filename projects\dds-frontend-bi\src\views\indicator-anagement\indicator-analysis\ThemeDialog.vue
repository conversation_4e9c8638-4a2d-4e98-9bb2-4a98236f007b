<template>
  <el-dialog
    title="选择分析主题"
    :visible.sync="themeDialogVisible"
    width="800px"
  >
    <div style="display: flex; align-items: center">
      <div class="label" style="margin-right: 10px; white-space: nowrap">
        快速检索:
      </div>
      <el-input
        v-model="query"
        style="width: 220px"
        @input="handleInput"
        placeholder="请输入主题名称、指标名称"
      ></el-input>
    </div>
    <CommonTable
      :page.sync="page"
      :loading="loading"
      :show-selection="false"
      :table-data="tableData"
      :show-batch-tag="false"
      :table-columns.sync="tableColumns"
      @onload="getTableData"
      height="300px"
    >
      <template #indNamesSlot="{ row }">
        <el-tooltip
          :content="row.indNames"
          placement="top"
          :disabled="!row.indNames"
        >
          <div class="indicator-names">
            {{ row.indNames }}
          </div>
        </el-tooltip>
      </template>
      <template #actionSlot="{ row }">
        <el-button
          type="text"
          style="margin-right: 10px"
          @click="handleLoad(row)"
        >
          加载
        </el-button>
        <el-popconfirm
          title="确定删除该主题吗？"
          @onConfirm="handleDelTheme(row)"
        >
          <el-button type="text" slot="reference">删除</el-button>
        </el-popconfirm>
      </template>
    </CommonTable>
  </el-dialog>
</template>

<script>
import CommonTable from "@/components/CommonTable"
import debounce from "lodash/debounce"

export default {
  components: { CommonTable },
  props: {},
  data() {
    return {
      themeDialogVisible: false,
      query: "",
      loading: false,
      page: {
        pageSize: 10,
        total: 0,
        currentPage: 1
      },
      tableColumns: [
        {
          label: "主题名称",
          prop: "themeName",
          visible: true,
          sortable: false
        },
        {
          label: "包含指标",
          prop: "indNames",
          width: 250,
          slot: true,
          visible: true,
          sortable: false
        },
        {
          label: "检索维度",
          prop: "dimNames",
          visible: true,
          sortable: false
        },
        {
          label: "创建人",
          prop: "creater",
          visible: true,
          sortable: false
        },
        {
          label: "操作",
          prop: "action",
          visible: true,
          slot: true,
          sortable: false
        }
      ],
      tableData: [],
    }
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    open() {
      this.themeDialogVisible = true
      this.getTableData()
    },
    handleInput: debounce(function () {
      this.page.currentPage = 1
      this.getTableData()
    }),
    // 删除主题
    async handleDelTheme(row) {
      await this.$httpBi.analyzeTheme.deleteAnalyzeTheme({
        themeCode: row.themeCode
      })
      this.$message.success("删除成功")
      this.getTableData()
    },
    // 加载主题
    async handleLoad(row) {
      const { data } = await this.$httpBi.analyzeTheme.loadAnalyzeTheme({
        themeCode: row.themeCode
      })
      this.$emit("loadTheme", data)
      this.themeDialogVisible = false
    },
    // 获取分析主题列表
    async getTableData() {
      this.loading = true
      const { data } = await this.$httpBi.analyzeTheme.getAnalyzeTheme({
        query: this.query,
        pageSize: this.page.pageSize,
        currentPage: this.page.currentPage
      })
      this.tableData = data.records
      this.page.total = data.total
      this.loading = false
    }
  }
}
</script>

<style scoped lang="scss">
.indicator-names {
  max-width: 250px;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
</style>
